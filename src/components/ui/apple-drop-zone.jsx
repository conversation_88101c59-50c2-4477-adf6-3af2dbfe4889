import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Upload, X, FileText, CheckCircle } from 'lucide-react';
import { cn } from '../../lib/utils';

const AppleDropZone = ({
  maxFiles = 10,
  acceptedFileTypes = ['.csv'],
  onFilesAdded,
  onConvert,
  onFileRemove,
  onConvertedFileClick,
  isConverting = false,
  convertedFiles = [],
  selectedFiles = [],
  errorMessage = null,
  className
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => 
      acceptedFileTypes.some(type => file.name.toLowerCase().endsWith(type.toLowerCase()))
    );
    
    if (validFiles.length > 0 && onFilesAdded) {
      onFilesAdded(validFiles.slice(0, maxFiles));
    }
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0 && onFilesAdded) {
      onFilesAdded(files.slice(0, maxFiles));
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("grid grid-cols-[1fr_auto_1fr] gap-6 p-5 px-6", className)} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif' }}>
      {/* Left: Drop Zone Area (40%) */}
      <div className="min-w-0">
        <div
          className={cn(
            "relative min-h-[200px] transition-all duration-200 cursor-pointer",
            selectedFiles.length === 0 ? "border-[1.5px] border-dashed rounded-[10px]" : "rounded-[10px]",
            selectedFiles.length === 0 && !isDragging && !errorMessage ? "border-[#c7c7cc] bg-transparent" : "",
            isDragging ? "border-[#007AFF] bg-[rgba(0,122,255,0.04)]" : "",
            errorMessage ? "border-[#FF3B30] bg-[rgba(255,59,48,0.04)]" : ""
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedFileTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
          />

          {selectedFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full py-10 px-6">
              <Upload className="h-6 w-6 text-[#86868b] mb-3" />
              <p className="text-[15px] font-[590] text-[#1d1d1f] mb-1 leading-[1.26667]">
                Drop files here or click to browse
              </p>
              <p className="text-[13px] font-normal text-[#86868b] leading-[1.38462]">
                Supports {acceptedFileTypes.join(', ')} files (max {maxFiles})
              </p>
            </div>
          ) : (
            <div className="p-4">
              <h3 className="text-[15px] font-[590] text-[#1d1d1f] mb-4 leading-[1.26667]">Selected Files</h3>
              <div className="space-y-0">
                {selectedFiles.map((file, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between py-2 border-b border-[#d1d1d6] last:border-b-0"
                  >
                    <div className="flex items-center flex-1 min-w-0">
                      <FileText className="h-4 w-4 text-[#86868b] mr-3 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="text-[13px] font-normal text-[#1d1d1f] truncate leading-[1.38462]">{file.name}</p>
                        <p className="text-[11px] font-normal text-[#86868b] leading-[1.36364]">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onFileRemove?.(index);
                      }}
                      className="p-1 text-[#86868b] hover:text-[#FF3B30] transition-colors duration-150 rounded"
                    >
                      <X className="h-3.5 w-3.5" />
                    </button>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {errorMessage && (
            <div className="absolute bottom-0 left-0 right-0 p-3 bg-[rgba(255,59,48,0.04)] border-t border-[#FF3B30] rounded-b-[10px]">
              <p className="text-[13px] font-normal text-[#FF3B30] leading-[1.38462]">{errorMessage}</p>
            </div>
          )}
        </div>
      </div>

      {/* Center: Convert Button (20%) */}
      <div className="flex-[1] flex items-center justify-center">
        <motion.button
          onClick={onConvert}
          disabled={selectedFiles.length === 0 || isConverting}
          className={cn(
            "px-4 py-2 rounded-md text-sm font-medium transition-all duration-200",
            "shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            selectedFiles.length > 0 && !isConverting
              ? "bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800"
              : "bg-gray-200 text-gray-400 cursor-not-allowed"
          )}
          whileHover={selectedFiles.length > 0 && !isConverting ? { scale: 1.02 } : {}}
          whileTap={selectedFiles.length > 0 && !isConverting ? { scale: 0.98 } : {}}
        >
          {isConverting ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Converting...
            </div>
          ) : (
            'Convert'
          )}
        </motion.button>
      </div>

      {/* Right: Converted Files Area (40%) */}
      <div className="flex-[2] min-w-0">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-[200px]">
          {convertedFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mb-3" />
              <p className="text-sm text-gray-500">Converted files will appear here</p>
            </div>
          ) : (
            <div>
              <h3 className="text-base font-medium text-gray-700 mb-3">Converted Files</h3>
              <div className="space-y-2">
                {convertedFiles.map((file, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer transition-colors duration-150"
                    onClick={() => onConvertedFileClick?.(file)}
                  >
                    <FileText className="h-4 w-4 text-red-500 mr-3" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-700 truncate">{file.filename}</p>
                      {file.success ? (
                        <p className="text-xs text-green-600">Converted successfully</p>
                      ) : (
                        <p className="text-xs text-red-600">{file.message || 'Conversion failed'}</p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { AppleDropZone };
